{"name": "bun-react-template", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "bun --hot src/index.tsx", "start": "NODE_ENV=production bun src/index.tsx", "build": "bun run build.ts", "check": "tsc --noEmit", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "lint:fix": "bun run format && eslint --fix --ext .js,.ts,.tsx .", "test:unit": "bun test", "test": "bun run test:unit"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@triplon/eslint-config": "file:/Users/<USER>/Work/Projects/ewildee/test-20240420-svelte-5/packages/eslint-config", "bun-plugin-tailwind": "^0.0.15", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^12.23.12", "lucide-react": "^0.284.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/bun": "latest", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/tailwindcss": "^3.1.0", "eslint": "^9.33.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14"}}