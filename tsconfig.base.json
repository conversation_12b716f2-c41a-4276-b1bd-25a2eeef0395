{
	"compilerOptions": {
		"target": "esnext",
		"lib": ["ESNext", "WebWorker"],
		"module": "Preserve",

		"outDir": "${configDir}/dist",

		"moduleResolution": "bundler",
		"resolvePackageJsonExports": true,
		"resolvePackageJsonImports": true,
		"allowSyntheticDefaultImports": true,
		"allowImportingTsExtensions": true,
		"resolveJsonModule": true,

		"moduleDetection": "force",
		"verbatimModuleSyntax": true,
		"isolatedModules": true,
		"composite": true,

		"allowJs": true,
		"checkJs": true,

		"erasableSyntaxOnly": true,
		"esModuleInterop": true,
		"skipLibCheck": true,

		"forceConsistentCasingInFileNames": true,
		"emitDeclarationOnly": true,
		"noEmitOnError": false,
		"incremental": true,
		"newLine": "lf",

		// Warn: When set to true, indirectly imported types are not resolved correctly.
		// "preserveSymlinks": true,

		// Cannot be used together with project references. Set individually in tsconfig.json files.
		// "noEmit": true,

		"sourceMap": true,
		"removeComments": false,

		"declaration": true,
		"declarationMap": true,
		"stripInternal": true,

		"noErrorTruncation": true,
		"pretty": true,

		"strict": true,
		"strictNullChecks": true,
		"strictPropertyInitialization": true,
		"exactOptionalPropertyTypes": true,
		"noImplicitReturns": false,
		"noImplicitAny": true,
		"noImplicitOverride": true,
		"noImplicitThis": true,
		"noUnusedLocals": false,
		"noUnusedParameters": false,
		"noFallthroughCasesInSwitch": true,
		"noUncheckedIndexedAccess": true,
		"noPropertyAccessFromIndexSignature": false,
		"allowUnreachableCode": false,
		"allowUnusedLabels": false,

		"preserveConstEnums": true
	},

	"exclude": [
		"**/__*.*",
		"node_modules/**",
		"${configDir}/node_modules/**",
		"build/**",
		"${configDir}/build/**",
		"dist/**",
		"${configDir}/dist/**"
	]
}
