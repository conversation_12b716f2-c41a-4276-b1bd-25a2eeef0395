import { motion } from 'framer-motion';
import { RotateCcw, Sparkles } from 'lucide-react';
import React, { useState, useCallback } from 'react';

import { FileUpload } from './components/FileUpload.tsx';
import { SummaryDisplay } from './components/SummaryDisplay.tsx';
import { TranscriptionDisplay } from './components/TranscriptionDisplay.tsx';
import { Button } from './components/ui/button.tsx';

interface TranscriptionMessage {
	id: string;
	speaker: 'Spreker 1' | 'Spreker 2';
	text: string;
	timestamp: string;
}

type AppState = 'initial' | 'uploading' | 'transcribing' | 'summarizing' | 'complete';

export function App() {
	const [state, setState] = useState<AppState>('initial');
	const [, setSelectedFile] = useState<File | undefined>();
	const [transcription, setTranscription] = useState<TranscriptionMessage[]>([]);
	const [summary, setSummary] = useState('');
	const [isGlobalDragging, setIsGlobalDragging] = useState(false);
	const [, setDragCounter] = useState(0);

	// Mock data for demonstration
	const mockTranscription: TranscriptionMessage[] = [
		{
			id: '1',
			speaker: 'Spreker 1',
			text: 'Hoi Sarah, bedankt dat je de tijd hebt genomen om vandaag met me te praten over de marketingcampagne.',
			timestamp: '0:00'
		},
		{
			id: '2',
			speaker: 'Spreker 2',
			text: 'Natuurlijk, Mike! Ik ben enthousiast om onze Q4 strategie te bespreken. Ik denk dat we geweldige kansen voor ons hebben.',
			timestamp: '0:05'
		},
		{
			id: '3',
			speaker: 'Spreker 1',
			text: 'Absoluut. Ik heb gekeken naar onze analytics van vorig kwartaal, en ik denk dat we meer moeten focussen op social media engagement.',
			timestamp: '0:12'
		},
		{
			id: '4',
			speaker: 'Spreker 2',
			text: 'Dat is een goed punt. Onze Instagram engagement is vorige maand met 45% gestegen. We moeten zeker voortbouwen op dat momentum.',
			timestamp: '0:20'
		},
		{
			id: '5',
			speaker: 'Spreker 1',
			text: 'Perfect. Wat denk je van het toewijzen van meer budget aan influencer partnerships?',
			timestamp: '0:28'
		}
	];

	const mockSummary =
		'Dit telefoongesprek was een strategische planning discussie tussen Mike en Sarah betreffende Q4 marketing initiatieven. Hoofdonderwerpen waren:\n\n• Social media engagement optimalisatie gebaseerd op sterke Q3 analytics prestaties\n• Instagram strategie met 45% engagement groei\n• Budget herverdelings overwegingen voor influencer partnerships\n• Focus op voortbouwen op recente social media successen\n\nDe toon was samenwerkend en vooruitkijkend, waarbij beide deelnemers enthousiasme toonden voor komende marketing kansen.';

	const handleFileSelect = async (file: File) => {
		setSelectedFile(file);
		setState('uploading');

		// Simulate file upload
		await new Promise(resolve => setTimeout(resolve, 2_000));
		setState('transcribing');

		// Simulate transcription process
		await new Promise(resolve => setTimeout(resolve, 3_000));
		setTranscription(mockTranscription);
		setState('summarizing');

		// Simulate summary generation
		await new Promise(resolve => setTimeout(resolve, 1_000));
		setSummary(mockSummary);
		setState('complete');
	};

	const handleRestart = () => {
		setState('initial');
		setSelectedFile(undefined);
		setTranscription([]);
		setSummary('');
	};

	const handleRegenerateSummary = async () => {
		setSummary('');
		setState('summarizing');

		// Simulate regeneration
		await new Promise(resolve => setTimeout(resolve, 2_000));
		setSummary(
			'Dit was een productief Q4 marketing strategie telefoongesprek tussen Mike en Sarah. De discussie was gericht op het benutten van succesvolle social media trends en het verkennen van nieuwe partnership mogelijkheden.\n\n• Data-gedreven aanpak: Q3 analytics gebruiken om toekomstige strategie te informeren\n• Instagram succes: 45% engagement toename biedt schaalmogelijkheden\n• Investeringsstrategie: Overweging van influencer partnerships voor uitgebreid bereik\n• Team afstemming: Beide deelnemers afgestemd op groei-gerichte aanpak\n\nHet gesprek reflecteert een data-geïnformeerde, samenwerkende aanpak van marketing planning met duidelijke uitvoerbare vervolgstappen.'
		);
		setState('complete');
	};

	// Global drag and drop handlers with proper counter logic
	const handleGlobalDragEnter = useCallback(
		(e: React.DragEvent) => {
			e.preventDefault();
			e.stopPropagation();

			// Only allow drag and drop when not currently processing
			if (
				!['uploading', 'transcribing'].includes(state) &&
				e.dataTransfer.types.includes('Files')
			) {
				setDragCounter(prev => prev + 1);
				setIsGlobalDragging(true);
			}
		},
		[state]
	);

	const handleGlobalDragOver = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
	}, []);

	const handleGlobalDragLeave = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();

		setDragCounter(prev => {
			const newCount = prev - 1;
			if (newCount === 0) {
				setIsGlobalDragging(false);
			}
			return newCount;
		});
	}, []);

	const handleGlobalDrop = useCallback(
		(e: React.DragEvent) => {
			e.preventDefault();
			e.stopPropagation();
			setIsGlobalDragging(false);
			setDragCounter(0);

			// Only allow drop when not currently processing
			if (['uploading', 'transcribing'].includes(state)) {
				return;
			}

			const files = e.dataTransfer.files;
			if (files.length > 0 && files[0]?.type.startsWith('audio/')) {
				void handleFileSelect(files[0]);
			}
		},
		[state, handleFileSelect]
	);

	const isFileProcessing = ['uploading', 'transcribing'].includes(state);
	const showTranscription = ['transcribing', 'summarizing', 'complete'].includes(state);
	const showSummary = ['summarizing', 'complete'].includes(state);
	const isMinimized = showTranscription;
	const isInitialState = state === 'initial';

	return (
		<motion.div
			className="relative flex min-h-screen flex-col p-6"
			animate={{
				backgroundColor: isGlobalDragging ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0)'
			}}
			transition={{ duration: 0.2 }}
			onDragEnter={handleGlobalDragEnter}
			onDragOver={handleGlobalDragOver}
			onDragLeave={handleGlobalDragLeave}
			onDrop={handleGlobalDrop}
		>
			{/* Background decorative elements */}
			<motion.div
				className="pointer-events-none fixed inset-0 overflow-hidden"
				animate={{
					opacity: isGlobalDragging ? 0.3 : 1
				}}
				transition={{ duration: 0.2 }}
			>
				<motion.div
					className="absolute left-10 top-10 h-4 w-4 rounded-full bg-pink-300 opacity-60"
					animate={{
						y: [0, -20, 0],
						scale: [1, 1.2, 1],
						opacity: [0.6, 0.8, 0.6]
					}}
					transition={{ duration: 4, repeat: Infinity }}
				/>
				<motion.div
					className="absolute right-20 top-32 h-3 w-3 rounded-full bg-purple-400 opacity-50"
					animate={{
						y: [0, 15, 0],
						scale: [1, 0.8, 1],
						opacity: [0.5, 0.7, 0.5]
					}}
					transition={{ duration: 3, repeat: Infinity, delay: 1 }}
				/>
				<motion.div
					className="absolute bottom-20 left-1/4 h-2 w-2 rounded-full bg-pink-400 opacity-70"
					animate={{
						x: [0, 10, 0],
						y: [0, -10, 0],
						opacity: [0.7, 0.9, 0.7]
					}}
					transition={{ duration: 5, repeat: Infinity, delay: 2 }}
				/>
				<motion.div
					className="absolute left-1/3 top-1/4 h-3 w-3 rounded-full bg-purple-300 opacity-40"
					animate={{
						x: [0, 25, -15, 0],
						y: [0, -15, 10, 0],
						scale: [1, 1.3, 0.9, 1],
						opacity: [0.4, 0.7, 0.5, 0.4]
					}}
					transition={{ duration: 7, repeat: Infinity, delay: 0.5 }}
				/>
				<motion.div
					className="absolute bottom-1/3 right-1/4 h-5 w-5 rounded-full bg-pink-200 opacity-45"
					animate={{
						rotate: [0, 180, 360],
						scale: [1, 0.7, 1.1, 1],
						opacity: [0.45, 0.6, 0.8, 0.45]
					}}
					transition={{ duration: 6, repeat: Infinity, delay: 1.5 }}
				/>
				<motion.div
					className="absolute left-16 top-2/3 h-2 w-2 rounded-full bg-purple-500 opacity-55"
					animate={{
						y: [0, -30, 15, 0],
						x: [0, 8, -5, 0],
						scale: [1, 1.4, 0.8, 1],
						opacity: [0.55, 0.8, 0.6, 0.55]
					}}
					transition={{ duration: 8, repeat: Infinity, delay: 3 }}
				/>
				<motion.div
					className="absolute right-1/3 top-16 h-3 w-3 rounded-full bg-pink-500 opacity-35"
					animate={{
						x: [0, -20, 12, 0],
						y: [0, 18, -8, 0],
						rotate: [0, -90, 180, 360],
						opacity: [0.35, 0.65, 0.5, 0.35]
					}}
					transition={{ duration: 9, repeat: Infinity, delay: 2.5 }}
				/>
				<motion.div
					className="absolute bottom-16 right-12 h-4 w-4 rounded-full bg-purple-200 opacity-50"
					animate={{
						scale: [1, 1.5, 0.6, 1.2, 1],
						rotate: [0, 120, 240, 360],
						opacity: [0.5, 0.75, 0.4, 0.65, 0.5]
					}}
					transition={{ duration: 10, repeat: Infinity, delay: 4 }}
				/>
				<motion.div
					className="absolute left-8 top-1/2 h-2 w-2 rounded-full bg-pink-600 opacity-60"
					animate={{
						x: [0, 15, -10, 20, 0],
						y: [0, -25, 5, -12, 0],
						scale: [1, 0.8, 1.3, 0.9, 1],
						opacity: [0.6, 0.85, 0.4, 0.7, 0.6]
					}}
					transition={{ duration: 11, repeat: Infinity, delay: 1.8 }}
				/>
			</motion.div>

			{/* Main container with flexible centering for initial state */}
			<motion.div
				layout
				className={`relative z-10 mx-auto flex w-full max-w-4xl flex-1 flex-col ${
					isInitialState ? 'items-center justify-center' : ''
				}`}
				transition={{ type: 'spring', stiffness: 300, damping: 30 }}
			>
				{/* Header section with smooth scaling and positioning */}
				<motion.div
					layout
					className={`text-center ${isInitialState ? 'mb-16' : 'mb-12'}`}
					transition={{ type: 'spring', stiffness: 300, damping: 30 }}
				>
					<motion.div
						animate={{
							rotate: [0, 5, -5, 0],
							scale: [1, 1.05, 1]
						}}
						transition={{ duration: 6, repeat: Infinity }}
						className="inline-block"
					>
						<motion.div layout transition={{ type: 'spring', stiffness: 300, damping: 30 }}>
							<Sparkles
								className={`mx-auto mb-4 text-pink-500 ${
									isInitialState ? 'mb-6 h-16 w-16' : 'h-12 w-12'
								}`}
							/>
						</motion.div>
					</motion.div>
					<motion.h1
						layout
						className={`mb-2 text-gray-800 ${isInitialState ? 'mb-4 text-5xl' : 'text-4xl'}`}
						transition={{ type: 'spring', stiffness: 300, damping: 30 }}
					>
						VoS Gesprekstranscriptie
					</motion.h1>
				</motion.div>

				{/* Content area that expands after initial state */}
				<motion.div
					layout
					className={`w-full ${isInitialState ? '' : 'flex-1'}`}
					transition={{ type: 'spring', stiffness: 300, damping: 30 }}
				>
					{/* File Upload with smooth transition */}
					<motion.div
						layout
						className={`flex justify-center ${isInitialState ? 'mb-16' : 'mb-3'}`}
						transition={{ type: 'spring', stiffness: 300, damping: 30 }}
					>
						<FileUpload
							onFileSelect={handleFileSelect}
							isUploading={isFileProcessing}
							isMinimized={isMinimized}
							isGlobalDragging={isGlobalDragging}
						/>
					</motion.div>

					{/* Content that appears after processing starts */}
					{!isInitialState && (
						<motion.div
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ type: 'spring', stiffness: 200, damping: 25, delay: 0.2 }}
						>
							{/* Restart button when minimized */}
							{isMinimized && (
								<motion.div
									initial={{ opacity: 0, scale: 0 }}
									animate={{ opacity: 1, scale: 1 }}
									transition={{ delay: 0.3, type: 'spring', stiffness: 300, damping: 25 }}
									className="mb-8 flex justify-center"
								>
									<Button
										onClick={handleRestart}
										variant="outline"
										className="border-pink-300 bg-white/70 text-gray-700 hover:border-pink-400 hover:bg-pink-50"
									>
										<RotateCcw className="mr-2 h-4 w-4" />
										Opnieuw Beginnen
									</Button>
								</motion.div>
							)}

							{/* Transcription Display */}
							<div className="space-y-6">
								<TranscriptionDisplay
									messages={transcription}
									isVisible={showTranscription}
									isLoading={state === 'transcribing'}
								/>

								{/* Summary Display */}
								<SummaryDisplay
									summary={summary}
									isVisible={showSummary}
									isStreaming={state === 'summarizing'}
									onRegenerate={handleRegenerateSummary}
								/>
							</div>
						</motion.div>
					)}
				</motion.div>
			</motion.div>

			{/* Footer */}
			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ delay: 1 }}
				className="relative z-10 mt-16 border-t border-pink-200/50 py-6 text-center"
			>
				<div className="flex items-center justify-center gap-3 text-gray-500">
					<motion.div
						animate={{
							rotate: 360,
							scale: [1, 1.1, 1]
						}}
						transition={{
							rotate: { duration: 12, repeat: Infinity, ease: 'linear' },
							scale: { duration: 3, repeat: Infinity }
						}}
					>
						<Sparkles className="h-4 w-4 text-pink-400" />
					</motion.div>
					<span>Voice of Secretary</span>
					<span className="text-pink-400">•</span>
					<span className="text-sm">v1.0.0</span>
				</div>
			</motion.div>
		</motion.div>
	);
}
