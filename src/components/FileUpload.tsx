import { motion } from 'framer-motion';
import { Sparkles } from 'lucide-react';
import { useCallback, useState } from 'react';

interface FileUploadProps {
	onFileSelect: (file: File) => void;
	isUploading: boolean;
	isMinimized: boolean;
	isGlobalDragging?: boolean;
}

export function FileUpload({
	isGlobalDragging = false,
	isMinimized,
	isUploading,
	onFileSelect
}: FileUploadProps) {
	const [isDragging, setIsDragging] = useState(false);

	const handleDrop = useCallback(
		(e: React.DragEvent) => {
			e.preventDefault();
			setIsDragging(false);

			const files = e.dataTransfer.files;
			if (files.length > 0 && files[0]?.type.startsWith('audio/')) {
				onFileSelect(files[0]);
			}
		},
		[onFileSelect]
	);

	const handleDragOver = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragging(true);
	}, []);

	const handleDragLeave = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragging(false);
	}, []);

	const handleFileInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const files = e.target.files;
			if (files && files.length > 0 && files[0]) {
				onFileSelect(files[0]);
			}
		},
		[onFileSelect]
	);

	// Use either local dragging or global dragging for visual state
	const showDragState = isDragging || isGlobalDragging;

	// Static sound wave heights for initial state
	const staticWaveHeights = isMinimized ? [8, 12, 16, 10, 6] : [12, 18, 24, 15, 9];

	return (
		<motion.div
			layout
			initial={{ scale: 1 }}
			animate={{
				scale: isMinimized ? 0.8 : 1,
				y: isMinimized ? -20 : 0
			}}
			transition={{ type: 'spring', stiffness: 300, damping: 30 }}
			className={`relative ${isMinimized ? 'mb-4' : 'mx-auto w-full max-w-lg'}`}
		>
			<motion.div
				className={`relative cursor-pointer rounded-2xl border-2 border-dashed text-center backdrop-blur-sm transition-all duration-300 ${
					showDragState
						? 'border-pink-400 bg-pink-100/50 shadow-xl shadow-pink-200/50'
						: 'border-pink-300 bg-white/70 hover:border-pink-400 hover:bg-pink-50/50'
				} ${isUploading ? 'pointer-events-none' : ''} ${isMinimized ? 'p-4 py-4' : 'p-12 py-16 shadow-2xl shadow-pink-100/30'} `}
				onDrop={handleDrop}
				onDragOver={handleDragOver}
				onDragLeave={handleDragLeave}
				whileHover={{ scale: isMinimized ? 1 : 1.02 }}
				whileTap={{ scale: isMinimized ? 1 : 0.98 }}
				animate={{
					scale: showDragState ? (isMinimized ? 0.85 : 1.05) : isMinimized ? 0.8 : 1
				}}
				transition={{ type: 'spring', stiffness: 300, damping: 30 }}
			>
				{/* Sparkle decorations */}
				<div className="pointer-events-none absolute inset-0">
					<motion.div
						className="absolute right-4 top-4 text-pink-400"
						animate={{
							rotate: 360,
							scale: showDragState ? [1, 1.4, 1] : [1, 1.2, 1]
						}}
						transition={{
							rotate: { duration: 20, repeat: Infinity, ease: 'linear' },
							scale: { duration: showDragState ? 4 : 8, repeat: Infinity }
						}}
					>
						<Sparkles className="h-4 w-4" />
					</motion.div>
					<motion.div
						className="absolute bottom-4 left-4 text-pink-300"
						animate={{
							rotate: -360,
							scale: showDragState ? [1, 1.5, 1] : [1, 1.3, 1]
						}}
						transition={{
							rotate: { duration: 25, repeat: Infinity, ease: 'linear' },
							scale: { duration: showDragState ? 6 : 12, repeat: Infinity }
						}}
					>
						<Sparkles className="h-3 w-3" />
					</motion.div>
				</div>

				<input
					type="file"
					accept="audio/*"
					title="Selecteer audiobestand"
					onChange={handleFileInputChange}
					className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
					disabled={isUploading}
				/>

				<motion.div
					className={`inline-block rounded-full bg-gradient-to-br from-pink-400 to-pink-600 shadow-lg transition-shadow hover:shadow-xl ${isMinimized ? 'p-4' : 'p-6'}`}
					animate={{
						scale: showDragState ? 1.1 : 1,
						rotate: showDragState ? [0, 5, -5, 0] : 0
					}}
					transition={{
						scale: { type: 'spring', stiffness: 300, damping: 30 },
						rotate: { duration: 0.6, repeat: showDragState ? Infinity : 0 }
					}}
				>
					{isUploading ? (
						// Fast processing sound wave animation
						<div
							className={`flex items-center justify-center gap-1 ${isMinimized ? 'h-8 w-8' : 'h-12 w-12'}`}
						>
							{[1, 2, 3, 4, 5].map(i => (
								<motion.div
									key={i}
									className="w-1.5 rounded-full bg-white"
									animate={{
										height: [12, 28, 12, 20, 12],
										opacity: [0.6, 1, 0.8, 1, 0.6]
									}}
									transition={{
										duration: 1.2,
										repeat: Infinity,
										delay: i * 0.1,
										ease: 'easeInOut'
									}}
									style={{ height: 12 }}
								/>
							))}
						</div>
					) : (
						// Bouncing wave animation that moves left to right and back
						<div
							className={`flex items-center justify-center gap-1 ${isMinimized ? 'h-8 w-8' : 'h-12 w-12'}`}
						>
							{[0, 1, 2, 3, 4].map(i => (
								<motion.div
									key={i}
									className={`rounded-full bg-white ${isMinimized ? 'w-1' : 'w-1.5'}`}
									animate={{
										height: showDragState
											? [
													(staticWaveHeights[i] ?? 12) + (isMinimized ? 4 : 8),
													(staticWaveHeights[i] ?? 12) + (isMinimized ? 12 : 20),
													(staticWaveHeights[i] ?? 12) + (isMinimized ? 8 : 16),
													(staticWaveHeights[i] ?? 12) + (isMinimized ? 12 : 20),
													(staticWaveHeights[i] ?? 12) + (isMinimized ? 4 : 8)
												]
											: [
													staticWaveHeights[i] ?? 12,
													(staticWaveHeights[i] ?? 12) +
														(isMinimized ? 8 : 12) * Math.sin(i * 0.8 + 0),
													(staticWaveHeights[i] ?? 12) +
														(isMinimized ? 8 : 12) * Math.sin(i * 0.8 + Math.PI / 2),
													(staticWaveHeights[i] ?? 12) +
														(isMinimized ? 8 : 12) * Math.sin(i * 0.8 + Math.PI),
													(staticWaveHeights[i] ?? 12) +
														(isMinimized ? 8 : 12) * Math.sin(i * 0.8 + (3 * Math.PI) / 2),
													staticWaveHeights[i] ?? 12
												],
										opacity: showDragState
											? [0.8, 1, 0.9, 1, 0.8]
											: [
													0.7,
													0.9 + 0.1 * Math.sin(i * 0.8 + 0),
													0.9 + 0.1 * Math.sin(i * 0.8 + Math.PI / 2),
													0.9 + 0.1 * Math.sin(i * 0.8 + Math.PI),
													0.9 + 0.1 * Math.sin(i * 0.8 + (3 * Math.PI) / 2),
													0.7
												]
									}}
									transition={{
										duration: showDragState ? 2 : 6,
										repeat: Infinity,
										delay: i * (showDragState ? 0.1 : 0.3),
										ease: 'easeInOut'
									}}
									style={{ height: staticWaveHeights[i] ?? 12 }}
								/>
							))}
						</div>
					)}
				</motion.div>

				<motion.div
					animate={isUploading ? { opacity: [0.7, 1, 0.7] } : { opacity: 1 }}
					transition={{
						duration: isUploading ? 2 : 0.3,
						repeat: isUploading ? Infinity : 0,
						ease: 'easeInOut'
					}}
				>
					{isUploading && (
						<h3 className={`text-gray-800 ${isMinimized ? 'mt-3' : 'mt-6'}`}>
							Bezig met verwerken...
						</h3>
					)}
					{!isUploading && !isMinimized && !showDragState && (
						<p className="mt-3 text-gray-600">Sleep je opname hierheen of klik om te bladeren</p>
					)}
					{!isUploading && !isMinimized && showDragState && (
						<motion.p
							className="mt-3 text-gray-800"
							animate={{ scale: [1, 1.05, 1] }}
							transition={{ duration: 1, repeat: Infinity }}
						>
							Loat moar los!
						</motion.p>
					)}
					{!isUploading && !isMinimized && !showDragState && (
						<p className="mt-2 text-sm text-gray-500">
							ondersteunt wav, mp3, m4a en andere audiobestanden
						</p>
					)}
				</motion.div>

				{/* Animated border glow effect */}
				{showDragState && (
					<motion.div
						className="absolute inset-0 rounded-2xl bg-gradient-to-r from-pink-400 via-purple-400 to-pink-400 opacity-20"
						animate={{
							backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
						}}
						transition={{ duration: 2, repeat: Infinity }}
						style={{ backgroundSize: '200% 200%' }}
					/>
				)}
			</motion.div>
		</motion.div>
	);
}
