import { motion } from 'framer-motion';
import { User, Bot, MessageSquare } from 'lucide-react';
import { useEffect, useRef } from 'react';

import { CopyButton } from './CopyButton.tsx';

interface TranscriptionMessage {
	id: string;
	speaker: 'Spreker 1' | 'Spreker 2';
	text: string;
	timestamp: string;
}

interface TranscriptionDisplayProps {
	messages: TranscriptionMessage[];
	isVisible: boolean;
	isLoading: boolean;
}

export function TranscriptionDisplay({
	isLoading,
	isVisible,
	messages
}: TranscriptionDisplayProps) {
	const containerRef = useRef<HTMLDivElement>(null);

	// Auto-scroll when transcription is ready (messages are loaded and not loading)
	useEffect(() => {
		if (messages.length > 0 && !isLoading && containerRef.current) {
			containerRef.current.scrollIntoView({
				behavior: 'smooth',
				block: 'start'
			});
		}
	}, [messages.length, isLoading]);

	if (!isVisible) return;

	return (
		<motion.div
			ref={containerRef}
			initial={{ opacity: 0, y: 50, height: 0 }}
			animate={{ opacity: 1, y: 0, height: 'auto' }}
			exit={{ opacity: 0, y: -50, height: 0 }}
			transition={{ type: 'spring', stiffness: 200, damping: 25 }}
			className="mx-auto mt-6 w-full max-w-2xl"
		>
			<div className="overflow-hidden rounded-2xl border border-pink-200 bg-white/80 shadow-xl shadow-pink-100/50 backdrop-blur-sm">
				<div className="bg-gradient-to-r from-pink-500 to-purple-600 p-4">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2 text-white">
							<MessageSquare className="h-5 w-5" />
							<h3>Transcriptie</h3>
						</div>

						{/* Copy button for entire transcription */}
						<div className="flex items-center gap-2">
							<CopyButton
								text={messages.map(msg => `${msg.speaker}: ${msg.text}`).join('\n\n')}
								className="border-white/30 bg-white/20 text-white hover:bg-white/30"
								disabled={isLoading || messages.length === 0}
							/>
						</div>
					</div>
				</div>

				<div className="max-h-96 overflow-y-auto p-4">
					{isLoading ? (
						<div className="space-y-4">
							{[1, 2, 3].map(i => (
								<motion.div
									key={i}
									initial={{ opacity: 0, x: -20 }}
									animate={{ opacity: 1, x: 0 }}
									transition={{ delay: i * 0.2 }}
									className="flex items-start gap-3"
								>
									<div className="h-8 w-8 animate-pulse rounded-full bg-gray-200" />
									<div className="flex-1 space-y-2">
										<div className="h-4 w-3/4 animate-pulse rounded bg-gray-200" />
										<div className="h-4 w-1/2 animate-pulse rounded bg-gray-200" />
									</div>
								</motion.div>
							))}
						</div>
					) : messages.length === 0 ? (
						<div className="py-8 text-center text-gray-500">
							<Bot className="mx-auto mb-2 h-12 w-12 text-gray-400" />
							<p>Transcriptie verschijnt hier...</p>
						</div>
					) : (
						<div className="space-y-4">
							{messages.map((message, index) => (
								<motion.div
									key={message.id}
									initial={{ opacity: 0, x: message.speaker === 'Spreker 1' ? -20 : 20 }}
									animate={{ opacity: 1, x: 0 }}
									transition={{ delay: index * 0.1 }}
									className={`flex items-start gap-3 ${
										message.speaker === 'Spreker 2' ? 'flex-row-reverse' : ''
									}`}
								>
									<div
										className={`flex h-8 w-8 items-center justify-center rounded-full ${
											message.speaker === 'Spreker 1'
												? 'bg-gradient-to-br from-blue-400 to-blue-600'
												: 'bg-gradient-to-br from-pink-400 to-pink-600'
										}`}
									>
										<User className="h-4 w-4 text-white" />
									</div>

									<div
										className={`max-w-[80%] flex-1 ${
											message.speaker === 'Spreker 2' ? 'text-right' : ''
										}`}
									>
										<div className="mb-1 flex items-center gap-2">
											<span className="text-sm text-gray-600">{message.speaker}</span>
											<span className="text-xs text-gray-400">{message.timestamp}</span>
										</div>
										<div className="group relative">
											<motion.div
												className={`rounded-2xl p-3 ${
													message.speaker === 'Spreker 1'
														? 'rounded-tl-md bg-gray-100 text-gray-800'
														: 'rounded-tr-md bg-gradient-to-br from-pink-100 to-purple-100 text-gray-800'
												}`}
											>
												<p>{message.text}</p>
											</motion.div>

											{/* Copy button - only visible on hover */}
											<div
												className={`absolute top-2 opacity-0 transition-opacity duration-200 group-hover:opacity-100 ${
													message.speaker === 'Spreker 2' ? 'left-2' : 'right-2'
												}`}
											>
												<CopyButton
													text={message.text}
													variant="ghost"
													size="sm"
													className="border border-gray-200 bg-white/80 text-gray-600 shadow-sm hover:bg-white hover:text-gray-800"
													iconSize="w-3 h-3"
												/>
											</div>
										</div>
									</div>
								</motion.div>
							))}
						</div>
					)}
				</div>
			</div>
		</motion.div>
	);
}
