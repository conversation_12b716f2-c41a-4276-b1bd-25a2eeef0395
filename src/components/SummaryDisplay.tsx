import { motion } from 'framer-motion';
import { <PERSON>rk<PERSON>, RotateCcw, FileText } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';

import { CopyButton } from './CopyButton.tsx';
import { Button } from './ui/button.tsx';

interface SummaryDisplayProps {
	summary: string;
	isVisible: boolean;
	isStreaming: boolean;
	onRegenerate: () => void;
}

export function SummaryDisplay({
	isStreaming,
	isVisible,
	onRegenerate,
	summary
}: SummaryDisplayProps) {
	const [displayedText, setDisplayedText] = useState('');
	const containerRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		if (!isStreaming || !summary) {
			setDisplayedText(summary);
			return;
		}

		setDisplayedText('');
		let currentIndex = 0;

		const interval = setInterval(() => {
			if (currentIndex < summary.length) {
				setDisplayedText(summary.slice(0, currentIndex + 1));
				currentIndex++;
			} else {
				clearInterval(interval);
			}
		}, 30);

		return () => clearInterval(interval);
	}, [summary, isStreaming]);

	// Auto-scroll when summary is ready or refreshed (summary exists and not streaming)
	useEffect(() => {
		if (summary && !isStreaming && containerRef.current) {
			// Small delay to ensure content is rendered before scrolling
			const timeoutId = setTimeout(() => {
				containerRef.current?.scrollIntoView({
					behavior: 'smooth',
					block: 'start'
				});
			}, 100);

			return () => clearTimeout(timeoutId);
		}
	}, [summary, isStreaming]);

	// Also scroll when the component becomes visible and has content
	useEffect(() => {
		if (isVisible && summary && !isStreaming && containerRef.current) {
			const timeoutId = setTimeout(() => {
				containerRef.current?.scrollIntoView({
					behavior: 'smooth',
					block: 'start'
				});
			}, 300); // Slightly longer delay for visibility changes

			return () => clearTimeout(timeoutId);
		}
	}, [isVisible, summary, isStreaming]);

	if (!isVisible) return;

	return (
		<motion.div
			ref={containerRef}
			initial={{ opacity: 0, y: 50, height: 0 }}
			animate={{ opacity: 1, y: 0, height: 'auto' }}
			exit={{ opacity: 0, y: -50, height: 0 }}
			transition={{ type: 'spring', stiffness: 200, damping: 25, delay: 0.2 }}
			className="mx-auto mt-6 w-full max-w-2xl"
		>
			<div className="overflow-hidden rounded-2xl border border-pink-200 bg-white/80 shadow-xl shadow-pink-100/50 backdrop-blur-sm">
				<div className="bg-gradient-to-r from-purple-500 to-pink-600 p-4">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2 text-white">
							<motion.div
								animate={isStreaming ? { rotate: 360 } : {}}
								transition={{ duration: 2, repeat: isStreaming ? Infinity : 0, ease: 'linear' }}
							>
								<Sparkles className="h-5 w-5" />
							</motion.div>
							<h3>Bericht</h3>
						</div>

						<div className="flex items-center gap-2">
							<Button
								onClick={onRegenerate}
								variant="outline"
								size="sm"
								className="border-white/30 bg-white/20 text-white hover:bg-white/30"
								disabled={isStreaming}
							>
								<RotateCcw className="h-4 w-4" />
							</Button>

							<CopyButton
								text={displayedText}
								className="border-white/30 bg-white/20 text-white hover:bg-white/30"
								disabled={isStreaming || !displayedText}
							/>
						</div>
					</div>
				</div>

				<div className="p-6">
					{isStreaming && !displayedText ? (
						<div className="space-y-3">
							<div className="flex items-center gap-2 text-gray-600">
								<motion.div
									animate={{ scale: [1, 1.2, 1] }}
									transition={{ duration: 1.5, repeat: Infinity }}
								>
									<Sparkles className="h-4 w-4 text-pink-500" />
								</motion.div>
								<span>Wordt aan gewerkt...</span>
							</div>
							<div className="space-y-2">
								{[1, 2, 3].map(i => (
									<motion.div
										key={i}
										initial={{ opacity: 0, width: 0 }}
										animate={{ opacity: 1, width: '100%' }}
										transition={{ delay: i * 0.3, duration: 0.8 }}
										className="h-3 animate-pulse rounded bg-gradient-to-r from-pink-200 via-purple-200 to-pink-200"
										style={{ width: `${Math.random() * 40 + 60}%` }}
									/>
								))}
							</div>
						</div>
					) : displayedText ? (
						<motion.div
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							className="prose prose-sm max-w-none"
						>
							<div className="relative">
								<div className="whitespace-pre-line leading-relaxed text-gray-800">
									{displayedText}
									{isStreaming && (
										<motion.span
											animate={{ opacity: [0, 1, 0] }}
											transition={{ duration: 1, repeat: Infinity }}
											className="ml-1 inline-block h-5 w-2 bg-pink-500"
										/>
									)}
								</div>
							</div>
						</motion.div>
					) : (
						<div className="py-8 text-center text-gray-500">
							<FileText className="mx-auto mb-2 h-12 w-12 text-gray-400" />
							<p>Hier wordt gewerkt...</p>
						</div>
					)}
				</div>
			</div>
		</motion.div>
	);
}
