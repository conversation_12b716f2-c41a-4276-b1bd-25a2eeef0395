import { motion } from 'framer-motion';
import { Copy, Check } from 'lucide-react';
import { useState } from 'react';

import { Button } from './ui/button.tsx';

interface CopyButtonProps {
	text: string;
	className?: string;
	disabled?: boolean;
	size?: 'sm' | 'default' | 'lg';
	variant?: 'default' | 'outline' | 'ghost';
	iconSize?: string;
}

export function CopyButton({
	className = '',
	disabled = false,
	iconSize = 'w-4 h-4',
	size = 'sm',
	text,
	variant = 'outline'
}: CopyButtonProps) {
	const [isCopied, setIsCopied] = useState(false);

	const handleCopy = async () => {
		if (!text || disabled) return;

		try {
			await navigator.clipboard.writeText(text);
			setIsCopied(true);
			setTimeout(() => setIsCopied(false), 2_000);
		} catch (error) {
			console.error('Failed to copy text:', error);
		}
	};

	return (
		<div className="relative">
			<Button
				onClick={handleCopy}
				variant={variant}
				size={size}
				className={className}
				disabled={disabled}
			>
				<motion.div
					animate={isCopied ? { scale: [1, 1.2, 1] } : { scale: 1 }}
					transition={{ duration: 0.3 }}
				>
					{isCopied ? <Check className={iconSize} /> : <Copy className={iconSize} />}
				</motion.div>
			</Button>
		</div>
	);
}
